
import io.spring.gradle.dependencymanagement.DependencyManagementPlugin
import io.spring.gradle.dependencymanagement.dsl.DependencyManagementExtension
import org.sonarqube.gradle.SonarExtension
import java.io.ByteArrayOutputStream

group = "com.multiplier"
version = "0.0.1-SNAPSHOT"

plugins {
    val kotlinVersion = "2.1.0"
    kotlin("jvm") version kotlinVersion
    kotlin("plugin.spring") version kotlinVersion
    kotlin("plugin.jpa") version kotlinVersion
    kotlin("plugin.serialization") version kotlinVersion
    kotlin("plugin.lombok") version kotlinVersion


    id("org.springframework.boot") version "3.3.9"
    id("org.liquibase.gradle") version "2.2.0"
    id("io.spring.dependency-management") version "1.0.11.RELEASE"

    id("java")
    id("jacoco")
    id("maven-publish")
    id("com.google.protobuf") version "0.9.4"
    id("org.sonarqube") version "4.0.0.2929"
}

subprojects {
    val ktorVersion by extra("3.0.3")
    val grpcVersion by extra("1.68.0")
    val grpcKotlinVersion by extra("1.4.1")
    val protobufVersion by extra("3.25.5")
    val multiplierCommonVersion by extra("7.6.45")

    apply<DependencyManagementPlugin>()
    extensions.getByType<DependencyManagementExtension>().apply {
        imports {
            mavenBom(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
        }

        dependencies {
            dependency("com.netflix.graphql.dgs:graphql-dgs-platform-dependencies:9.1.3")
            dependency("org.codehaus.janino:janino:3.1.8")
            dependency("net.logstash.logback:logstash-logback-encoder:7.2") // Structured (JSON) logging

            dependency("net.devh:grpc-spring-boot-starter:3.1.0.RELEASE")
            dependency("io.sentry:sentry-spring-boot-starter:6.4.2")
            dependency("org.springframework.kafka:spring-kafka:3.3.0")

            dependency("com.multiplier.platform:spring-starter:2.3.6")
            dependency("com.multiplier.platform.utility:db-updater-client:0.2.90")

            dependency("com.multiplier:core-service-schema:1.1.215")
            dependency("com.multiplier:contract-service-schema:1.15.59")
            dependency("com.multiplier:member-service-schema:1.1.125")
            dependency("com.multiplier:country-service-schema:1.9.32")
            dependency("com.multiplier:contract-offboarding-service-schema:1.8.59")
            dependency("com.multiplier:customer-integration-service-graph:1.14.898-FND-3402-field-mappings-Clean-up-old-field-mappings-SNAPSHOT")
            dependency("com.multiplier:bulk-upload-service-graph:1.14.807")
            dependency("com.multiplier:pigeon-service-schema:1.1.29")
            dependency("com.multiplier:pigeon-service-client:1.1.29")
            dependency("com.multiplier:payroll-service-schema:1.1.590")
            dependency("com.multiplier:expense-service-schema:1.3.31")
            dependency("com.multiplier:company-service-schema:3.5.381")
            dependency("com.multiplier:timeoff-service-schema:2.3.2")
            dependency("com.multiplier.grpc:grpc-common:1.2.34")
            dependency("com.multiplier:bulk-upload-service-grpc-schema:1.2.6")
            dependency("com.multiplier:contract-onboarding-service-schema:1.0.77")
            dependency("com.multiplier:pay-se-schema:2.1.8")
            dependency("com.multiplier:field-mapping-service-grpc-schema:0.0.7")
            dependency("com.multiplier:growthbook-sdk:1.1.3")
            dependency("com.multiplier.platform:spring-jobs-starter:2.3.6")
            dependency("com.multiplier:document-generation-schema:2.0.24")

            dependency("com.multiplier.common:aws:$multiplierCommonVersion")
            dependency("com.multiplier.messaging:bulk-upload-service:0.2.98")
            dependency("org.liquibase:liquibase-core:4.16.1")
            dependency("com.vladmihalcea:hibernate-types-52:2.19.2")
            dependency("org.zalando:problem-spring-web:0.27.0")
            dependency("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4")
            dependency("io.jsonwebtoken:jjwt-api:0.11.5")
            dependency("io.jsonwebtoken:jjwt-impl:0.11.5")
            dependency("io.jsonwebtoken:jjwt-jackson:0.11.5")
            dependency("com.github.daniel-shuy:kafka-protobuf-serde:2.2.0")
            dependency("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.2")
            dependency("io.ktor:ktor-client-core:$ktorVersion")
            dependency("io.ktor:ktor-client-content-negotiation:$ktorVersion")
            dependency("io.ktor:ktor-serialization-jackson:$ktorVersion")
            dependency("io.ktor:ktor-client-jackson:$ktorVersion")
            dependency("io.ktor:ktor-client-apache:$ktorVersion")
            dependency("io.ktor:ktor-client-okhttp:$ktorVersion")
            dependency("io.ktor:ktor-client-auth:$ktorVersion")
            dependency("io.ktor:ktor-client-resources:$ktorVersion")
            dependency("io.ktor:ktor-client-mock:$ktorVersion")
            dependency("dev.merge:client:2.0.3-multiplier-patch-3")
            dependency("net.minidev:json-smart:2.4.9")
            dependency("commons-validator:commons-validator:1.9.0")
            dependency("commons-beanutils:commons-beanutils:1.11.0")
            dependency("org.apache.poi:poi-ooxml:5.4.0")


            dependency("com.google.protobuf:protobuf-kotlin:$protobufVersion")
            dependency("com.google.protobuf:protobuf-java:$protobufVersion")
            dependency("io.grpc:grpc-kotlin-stub:$grpcKotlinVersion")
            dependency("io.grpc:grpc-protobuf:$grpcVersion")
            dependency("io.grpc:grpc-core:$grpcVersion")
            dependency("io.grpc:grpc-stub:$grpcVersion")
            dependency("io.grpc:grpc-services:$grpcVersion")
            dependency("io.grpc:grpc-netty-shaded:$grpcVersion")
            dependency("io.grpc:grpc-api:$grpcVersion")
            dependency("io.grpc:grpc-bom:$grpcVersion")


            dependency("com.ninja-squad:springmockk:3.1.1")
            dependency("org.springframework.boot:spring-boot-starter-webflux:2.7.4")
            dependency("com.h2database:h2:2.2.220")
            dependency("com.github.javafaker:javafaker:1.0.2")

            dependency("org.projectlombok:lombok:1.18.28")
            dependency("org.json:json:20231013")
            dependency("org.springframework.cloud:spring-cloud-starter-openfeign:4.1.3")
            dependency("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4")
            dependency("org.bouncycastle:bcprov-jdk18on:1.78")
            dependency("com.google.guava:guava:32.0.0-android")
            dependency("com.graphql-java:graphql-java:22.3")

            dependency("org.testcontainers:testcontainers:1.17.6")
            dependency("org.testcontainers:junit-jupiter:1.17.6")
            dependency("org.testcontainers:postgresql:1.17.6")
            dependency("org.testcontainers:kafka:1.20.6")

            dependency("io.kotest:kotest-assertions-core:5.9.1")
            dependency("io.kotest:kotest-assertions-json:5.9.1")
        }
    }

    apply<JacocoPlugin>()
    tasks.withType<JacocoReport> {
        dependsOn(tasks.test)

        reports {
            html.required.set(false)
            xml.required.set(false)
        }
    }
}

allprojects {

    apply<IdeaPlugin>()

    repositories {
//        gradlePluginPortal()
        mavenCentral()
        mavenLocal()
        multiplierRepository()
    }

    plugins.withType<MavenPublishPlugin> {
        publishing {
            repositories {
                multiplierRepository()
            }
        }
    }
}

sonarqube {
    properties {
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_customer-integration-service")
        property("sonar.projectName", "customer-integration-service")
        property("sonar.organization", "multiplier")
        property("sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco.xml")
    }
}

tasks.withType<Wrapper> {
    gradleVersion = "8.1.1"
}

tasks.withType<Jar> {
    enabled = false
}

configure<SonarExtension> {
    val exclusions = listOf(
        "**/multiplier/core/common/*",
        "**/multiplier/core/common/db/**",
        "**/dto/**",
        "**/multiplier/core/common/exceptionhandler/*",
        "**/constant/**",
        "**/model/**",
        "**/enums/**",
        "**/scalar/**",
        "**/multiplier/core/payroll/core/docgen/payslip/**",
        "**/multiplier/core/payroll/graphql/typeWrappers/**",
        "**/multiplier/core/payroll/report/domain/**",
        "**/multiplier/core/payroll/service/builder/**",
        "**/multiplier/core/payroll/workflow/domain/**",
        "**/exception/**",
        "**/*Exception.*",
    )
    properties {
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_customer-integration-service")
        property("sonar.projectName", "customer-integration-service")
        property("sonar.organization", "multiplier")
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.coverage.exclusions", exclusions)
    }
}

tasks.withType<JacocoReport> {
    this.let {
        subprojects {
            tasks.withType<JacocoReport> {
                it.dependsOn(this)
                it.executionData(executionData)
                it.sourceSets(sourceSets.named("main").get())
            }
        }

        reports {
            xml.required.set(true)
            html.required.set(false)
            xml.outputLocation.set(File("${buildDir}/reports/jacoco.xml"))
        }
    }
}

val versionName by extra {
    if (project.hasProperty("publishVersion")) "${project.properties["publishVersion"]}"
    else ByteArrayOutputStream().use {
        exec {
            commandLine("git", "describe", "--tags", "--always")
            standardOutput = it
        }
        it.toString().trim().removePrefix("v")
    }
}

fun RepositoryHandler.multiplierRepository() {
    maven(url = "https://multiplier-artifacts-778085304246.d.codeartifact.ap-southeast-1.amazonaws.com/maven/multiplier-artifacts/") {
        credentials {
            username = "aws"
            password = generateCodeartifactToken()
        }
    }
}


fun generateCodeartifactToken(): String {
    logger.info("Generating codeartifact token...")
    return try {
        providers.environmentVariable("CODEARTIFACT_AUTH_TOKEN").getOrElse("").ifBlank {
            val profile = providers.environmentVariable("MULTIPLIER_AWS_PROFILE").getOrElse("default")
            ByteArrayOutputStream().use {
                exec {
                    commandLine(
                        "aws",
                        "codeartifact",
                        "get-authorization-token",
                        "--domain",
                        "multiplier-artifacts",
                        "--domain-owner",
                        "778085304246",
                        "--query",
                        "authorizationToken",
                        "--output",
                        "text",
                        "--profile",
                        profile,
                    )
                    standardOutput = it
                }
                it.toString().trim()
            }
        }
    } catch (ex: Exception) {
        println("Couldn't load codeartifact token.")
        ""
    }
}

fun String?.ifNullOrBlank(defaultValue: () -> String) = (this ?: "").ifBlank(defaultValue)
