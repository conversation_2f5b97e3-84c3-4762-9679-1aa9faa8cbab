package com.multiplier.integration.sync

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.integration.adapter.model.knit.EmployeeData

import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.sync.model.EventData
import com.multiplier.integration.sync.model.ExpenseData
import com.multiplier.integration.types.Address
import com.multiplier.integration.types.Capability
import com.multiplier.integration.types.CountryCode
import com.multiplier.integration.types.CurrencyCode
import com.multiplier.integration.types.DocumentFolderDetail
import com.multiplier.integration.types.FieldMappingV2
import com.multiplier.integration.types.IntegrationFieldsMappingOutputV2
import com.multiplier.integration.types.IntegrationFieldsMappingContractorOutput
import com.multiplier.integration.types.LegalEntity
import com.multiplier.integration.types.LegalEntityStatus
import com.multiplier.integration.types.LegalMappingStatus
import com.multiplier.integration.types.UnmappedField
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DataMapper {
    fun map(event: JpaReceivedEvent): EventData {
        val receivedEvent = objectMapper.readTree(event.data)
        val eventDataJsonNode: JsonNode = receivedEvent["eventData"]
        val eventDataJsonString: String = objectMapper.writeValueAsString(eventDataJsonNode)
        val eventData: EventData = objectMapper.readValue(eventDataJsonString, EventData::class.java)
        return eventData
    }

    fun map(event: JsonNode): EventData {
        val eventDataJsonNode: JsonNode = event["eventData"]
        val eventDataJsonString: String = objectMapper.writeValueAsString(eventDataJsonNode)
        val eventData: EventData = objectMapper.readValue(eventDataJsonString, EventData::class.java)
        return eventData
    }

    fun map(employeeData: String?): EmployeeData {
        return objectMapper.readValue(employeeData, EmployeeData::class.java)
    }

    fun mapExpenseData(event: JpaReceivedEvent): ExpenseData {
        val receivedEvent = objectMapper.readTree(event.data)
        val eventDataJsonNode: JsonNode = receivedEvent["eventData"]
        val eventDataJsonString: String = objectMapper.writeValueAsString(eventDataJsonNode)
        return objectMapper.readValue(eventDataJsonString, ExpenseData::class.java)
    }

    companion object {
        var objectMapper = ObjectMapper().apply {
            registerKotlinModule()
            configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true)
            configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
            configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        }
    }

    fun map(grpcLegalEntity: CompanyOuterClass.LegalEntity?): LegalEntity? {
        if (grpcLegalEntity == null) {
            return null
        }
        return LegalEntity.newBuilder()
            .id(grpcLegalEntity.id)
            .address(
                Address.newBuilder()
                .key(grpcLegalEntity.address.key)
                .street(grpcLegalEntity.address.street)
                .line1(grpcLegalEntity.address.line1)
                .line2(grpcLegalEntity.address.line2)
                .city(grpcLegalEntity.address.city)
                .state(grpcLegalEntity.address.state)
                .province(grpcLegalEntity.address.province)
                .country(CountryCode.valueOf(grpcLegalEntity.address.country))
                .zipcode(grpcLegalEntity.address.zipcode)
                .postalCode(grpcLegalEntity.address.postalCode)
                .build())
            .legalName(grpcLegalEntity.legalName)
            .currency(nullIfEmpty(grpcLegalEntity.currencyCode))
            .phone(grpcLegalEntity.phone)
            .registrationNo(grpcLegalEntity.registrationNo)
            .capabilities(grpcLegalEntity.capabilitiesList.stream().map { Capability.valueOf(it) }.toList())
            .status(LegalEntityStatus.valueOf(grpcLegalEntity.status.name))
            .build()
    }

    private fun nullIfEmpty(currency: String?) =
        if (currency.isNullOrEmpty()) {
           null
        } else {
            CurrencyCode.valueOf(currency)
        }



    fun map(folderId: String, folderName: String): DocumentFolderDetail {
        return DocumentFolderDetail.newBuilder()
            .id(folderId)
            .label(folderName)
            .build()
    }

    fun map(
        key: String?,
        label: String?,
        isMappedByThirdParty: Boolean,
        fieldId: String?,
        type: String?,
        fieldFromApp: String?,
        isCustomField: Boolean
    ): UnmappedField.Builder {
        return UnmappedField.newBuilder()
            .key(key)
            .label(label)
            .isMappedByThirdParty(isMappedByThirdParty)
            .fieldId(fieldId)
            .type(type)
            .fieldFromApp(fieldFromApp)
            .isCustomField(isCustomField)
    }

    fun map(
        profileId: UUID,
        integrationId: Long,
        matchedLegalEntity: CompanyOuterClass.LegalEntity,
        mappingStatus: String,
        sourceFields: List<FieldMappingV2>,
        companyId: Long,
        targetFields: List<FieldMappingV2>
    ): IntegrationFieldsMappingOutputV2 = IntegrationFieldsMappingOutputV2.newBuilder()
        .profileId(profileId)
        .entityId(matchedLegalEntity.id)
        .integrationId(integrationId)
        .companyId(companyId)
        .sourceFields(sourceFields)
        .targetFields(targetFields)
        .legalEntity(map(matchedLegalEntity))
        .entityMappingStatus(LegalMappingStatus.valueOf(mappingStatus))
        .build()

    fun  map(
        profileId: UUID,
        integrationId: Long,
        mappingStatus: String,
        companyId: Long,
        sourceFields: List<FieldMappingV2>,
        targetFields: List<FieldMappingV2>
    ): IntegrationFieldsMappingContractorOutput = IntegrationFieldsMappingContractorOutput.newBuilder()
        .profileId(profileId)
        .companyId(companyId)
        .integrationId(integrationId)
        .sourceFields(sourceFields)
        .targetFields(targetFields)
        .entityMappingStatus(LegalMappingStatus.valueOf(mappingStatus))
        .build()
}
