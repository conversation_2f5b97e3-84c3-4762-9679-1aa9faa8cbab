extend type Query {
    getUniqueAddonDropdownData: AddonDropdownData @authorize(operations: ["use.operations.benefits"])
    getAllAddons(filter: AddonFilters!, benefitPackageId: UUID): [Addons] @authorize(operations: ["use.operations.benefits"], company: ["view.company.insurance"])
    addonSheetDataDownload(filter: AddonFilters!): DocumentReadable @authorize(operations: ["use.operations.benefits"])
    addonTemplateDownload: DocumentReadable @authorize(operations: ["use.operations.benefits"])
    getAllContractAddons(contractId: ID!): ContractAddonResponse @authorize(operations: ["use.operations.benefits"], company: ["view.company.insurance"], member: ["use.member.dashboard"])
}

extend type Mutation {
    uploadAddonSheetData(file: Upload!): AddonResponse @authorize(operations: ["use.operations.benefits"])
    upsertContractAddons(input: ContractAddonsInput!): TaskResponse @authorize(operations: ["use.operations.benefits"], company: ["view.company.insurance"])
}

type AddonResponse {
    response: TaskResponse
}

type AddonDropdownData {
    partnerNames: [String],
    addonTypes: [String],
    addonVariants: [String]
}

type Addons {
    id:ID!,
    type: String!,
    name: String,
    description: String,
    partnerName: String,
    partnerDetail: PartnerDetails,
    features: [AddonFeature],
    variants: [AddonVariants],
    startDate: DateTime,
    endDate: DateTime,
    factsheetUrl: String,
    onboardingKitUrl: String,
    status: String,
    validity: String
}

type PartnerDetails {
    id: ID!,
    partnerName: String!,
    partnerEmail: String,
    partnerWebsite: String,
    partnerPhoneNo: String,
    status: String
}

type AddonFeature {
    key: String,
    description: String
}

type AddonVariants {
    id: ID!,
    identifier: String!,
    description: String,
    status: String,
    cost: Float,
    isBundled: Boolean,
    variantCountryMappings: [VariantCountryMapping]
}

type VariantCountryMapping {
    id: ID!,
    status: String,
    currencyCode: CurrencyCode,
    countryCode: CountryCode,
    countryMappingStatus: String,
    localCurrency: CurrencyCode,
    visibleTo: String,
    isRecommended: Boolean,
    annualPriceInUsd: Float,
    monthlyPriceInUsd: Float,
    annualPriceInLocalCurrency: Float,
    monthlyPriceInLocalCurrency: Float,
    monthlyPlatformFeeInUsd: Float,
    monthlyPlatformFeeInLocalCurrency: Float,
    contractAddonStatus: String
}

input AddonFilters {
    addonIds: [ID],
    type: String,
    country: CountryCode,
    status: String,
    partnerName: String,
    startDate: DateRange,
    endDate: DateRange,
    variantCountryMappingStatus: String
}

type ContractAddonResponse {
    status: ContractAddonStatus, @deprecated(reason: "Not used")
    addons: [Addons],
    billingInfo: BillingInfo
}

type BillingInfo {
    premiumFrequency: AddonPremiumFrequency,
    platformFee: Float
    totalFee: Float
    currency: CurrencyCode
}

input ContractAddonsInput {
    contractId: ID!,
    variantIds: [ID]!,
    country: CountryCode!,
    premiumFrequency: AddonPremiumFrequency!,
    benefitPackageId: UUID
}

enum AddonPremiumFrequency {
    MONTHLY
    ANNUALLY
}

enum ContractAddonStatus {
    NEW
    ACTIVE
    DEACTIVATED
}
