extend type Query {
    """
    Get vendor summary for a payroll cycle.
    Returns aggregated vendor information including vendor-wise breakdowns,
    total contracts, vendor configurations, and total amounts.
    """
    vendorSummary(input: VendorSummaryInput!): PayrollVendorsSummary 
        @authorize(operations: ["view.operations.payroll-vendor"], company: ["view.company.payroll-vendor"])
}

"""
Input for retrieving vendor summary information for a specific payroll cycle.
"""
input VendorSummaryInput {
    """
    The ID of the payroll cycle to get vendor summary for.
    """
    payrollCycleId: ID!
}

"""
Represents a summary of vendor aggregations for a payroll cycle.
Contains a list of vendor aggregations with their respective details,
total number of contracts, and vendor configuration.
"""
type PayrollVendorsSummary {
    """
    List of vendor aggregations for the payroll cycle.
    Each aggregation contains vendor-specific totals and contract counts.
    """
    vendorWiseAggregations: [VendorWiseAggregation!]!
    
    """
    Total number of contracts in the payroll cycle.
    """
    totalNumberOfContracts: Int!
    
    """
    Vendor configuration for the payroll cycle.
    Contains vendor details and their supported payroll components.
    """
    vendorConfig: [VendorConfig!]!
    
    """
    Total amount across all vendors in the payroll cycle.
    """
    total: Float!
}

"""
Represents a vendor-wise aggregation in the vendor summary.
Contains vendor name, payroll components, total amount, and number of contracts.
"""
type VendorWiseAggregation {
    """
    The name of the vendor.
    """
    name: String!
    
    """
    List of payroll component names handled by this vendor.
    """
    payrollComponents: [String!]!
    
    """
    The total amount for this vendor across all contracts.
    """
    totalAmount: Float!
    
    """
    The number of contracts that have payments to this vendor.
    """
    numberOfContracts: Int!
}

"""
Data Transfer Object for vendor configuration.
Contains vendor identification, location, and supported payroll components.
"""
type VendorConfig {
    """
    Unique identifier for the vendor.
    """
    id: ID!
    
    """
    The name of the vendor.
    """
    vendorName: String!
    
    """
    The country code where the vendor operates.
    """
    countryCode: CountryCode!
    
    """
    List of payroll component names that this vendor supports.
    """
    payrollComponents: [String!]!
}
