# Reference: https://www.notion.so/usemultiplier/Scheduled-Payouts-1f9f9e1ba7a7804b903dc9c58155a07d

extend type Query { #TODO: Update permissions once confirmed
    # Get all payout schedules for a company
    getPayoutSchedules(companyId: ID!): [PayoutSchedule!]! @authorize(company: ["view.company.payment.bundle"])

    # Get a specific payout schedule by ID
    getPayoutScheduleById(scheduleId: ID!): PayoutSchedule @authorize(company: ["view.company.payment.bundle"])

    # Get payout schedule runs for a schedule
    getPayScheduleRunsByScheduleId(scheduleId: ID!): [PayoutScheduleRun!]! @authorize(company: ["view.company.payment.bundle"])

    # Get payout timeline based on a specific date
    getPayScheduleTimelineByDate(payoutDate: Date, scheduleId: ID): PayoutTimeline! @authorize(company: ["view.company.payment.bundle"])

    # Get contracts assigned to a specific payout schedule
    getContractsForPayoutSchedule(scheduleId: ID!): [PayoutScheduleContract!]! @authorize(company: ["view.company.payment.bundle"])
}

extend type Mutation { #TODO: Update permissions once confirmed
    # Create a new payout schedule
    createPayoutSchedule(input: CreatePayoutScheduleInput!): PayoutSchedule! @authorize(company: ["create.company.payment.bundle"])

    # Update an existing payout schedule
    updatePayoutSchedule(input: UpdatePayoutScheduleInput!): PayoutSchedule! @authorize(company: ["create.company.payment.bundle"])

    # Delete a payout schedule
    deletePayoutSchedule(scheduleId: ID!): PayoutSchedule! @authorize(company: ["create.company.payment.bundle"])

    # Assign a contract to a payout schedule
    assignScheduleToContract(contractId: ID!, scheduleId: ID!): PayoutScheduleContract! @authorize(company: ["create.company.payment.bundle"])

    # Unassign a contract from its payout schedule
    unassignScheduleFromContract(contractId: ID!): PayoutScheduleContract! @authorize(company: ["create.company.payment.bundle"])

    # Bulk assign contracts to a payout schedule
    bulkAssignScheduleToContracts(contractIds: [ID!]!, scheduleId: ID!): [PayoutScheduleContract!]! @authorize(company: ["create.company.payment.bundle"])
}

type PayoutSchedule {
    id: ID!
    companyId: ID!
    name: String!
    frequency: PayoutFrequency!
    payoutDates: [PayoutDateConfig!]!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
    payoutScheduleContracts: [PayoutScheduleContract]
    createdAt: DateTime!
    updatedAt: DateTime!
}

type PayoutScheduleContract {
    id: ID!
    scheduleId: ID!
    payoutAmount: Float # computed amount based on billing rate, frequency and schedule payout frequency
    payoutCurrency: CurrencyCode
    contract: Contract @provides(fields: "id")
    createdAt: DateTime!
    updatedAt: DateTime!
}

enum PayoutFrequency {
    MONTHLY
    SEMI_MONTHLY
    EVERY_TWO_WEEKS
}

type PayoutScheduleRun {
    id: ID!
    scheduleId: ID!
    description: String # Month and date description
    invoices: [MemberPayable]
}

type PayoutDateConfig {
    dates: [Int!]  # For MONTHLY, SEMI_MONTHLY (e.g., [1, 16] for 1st and 16th)
    weekday: Int   # For EVERY_TWO_WEEKS (0-6, Sunday-Saturday)
}

type PayoutTimeline {
    invoiceGenerationDate: Int!
    invoiceApprovalByDate: Int!
    payInDate: Int!
    payoutDate: Int!
}

input CreatePayoutScheduleInput {
    companyId: ID!
    frequency: PayoutFrequency!
    payoutDates: [PayoutDateConfigInput!]!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
}

input UpdatePayoutScheduleInput {
    id: ID!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
}

input PayoutDateConfigInput {
    dates: [Int!]
    weekday: Int
}
