extend type Query {
    contractJobCode(filter: JobCodeFilters!): String @authorize(company: ["view.company.contract.job-code"], operations: ["view.operations.contract.job-code"])
}

extend type Mutation {
    # Domain
    contractUpdateCompensation(id: ID!, input: ContractUpdateCompensationInput!): Contract @authorize(company: ["update.company.contract.compensation"], operations: ["update.operations.contract.compensation"])
    contractUpdateDeductions(id: ID!, input: [DeductionInput!]!): [Deduction] @authorize(company: ["update.company.contract.compensation"], operations: ["update.operations.contract.compensation"], member: ["update.member.contract.compensation.deduction"])
    contractDeleteCompensationGrant(contractId: ID!, grantId: ID!): [Grant] @authorize(company: ["delete.company.contract.compensation"], operations: ["delete.operations.contract.compensation"])
    contractDeleteProbationBasePay(contractId: ID!): Contract @authorize(company: ["delete.company.contract.compensation"], operations: ["delete.operations.contract.compensation"])
    updateCompensationPaymentInfo(input: UpdateCompensationPayInfoInput!): TaskResponse @authorize(operations: ["update.operations.contract.compensation"])

    """
    This directly updates the compensation record, setting columns based on the fields specified in the input (null/omitted = ignored).
    This means, to really set a column to `null`, you have to raise db-update PRs for now (rare demand).
    No other side effect except `compensationUpdateKafkaPublisher.publishUpdate(contractId);`.
    More details on each field: check comments in CompensationRecordUpdateInput.

    Intended for Product Ops only (alternative of database-updater PRs). Once we have a better way to do this, we can deprecate this
    """
    compensationRecordUpdate(input: CompensationRecordUpdateInput!): TaskResponse @authorize(operations: ["update.operations.contract.compensation"])

    # Intended for Product Ops only (alternative of database-updater PRs)
    compensationRecordInsert(input: CompensationRecordInsertInput!): TaskResponse @authorize(operations: ["update.operations.contract.compensation"])

    # V2 update compensation endpoint to support compensation via compensation service
    contractUpdateCompensationV2(id: ID!, input: ContractUpdateCompensationV2Input!): ContractUpdateCompensationV2Response @authorize(company: ["update.company.contract.compensation"], operations: ["update.operations.contract.compensation"])
}

# --------------------------------------
# Query types....
# --------------------------------------

input JobCodeFilters {
    countryCode: CountryCode!
    contractType: ContractType!
    probationBasePay: FixedPayComponentInput,
    postProbationBasePay: FixedPayComponentInput,
}
# --------------------------------------
# Mutation types....
# --------------------------------------

# Domain
input ContractUpdateCompensationInput {
    basePay: FixedPayComponentInput!
    probationBasePay: FixedPayComponentInput
    other: [FixedPayComponentInput] # @deprecated deprecating it to support multiple types of component inputs, please start using specific payComponents - otherFixedPayComponents, otherPercentPayComponents, etc..
    otherFixedPayComponents: [FixedPayComponentInput]
    otherPercentPayComponents: [PercentPayComponentInput]
    additional: String
    payrollStart: DateTime  # TODO: Fix, Not being set in BE.... @TR
    payrollConfigId: ID
    payType: PayType
    grant: [GrantInput]
    deductions: [DeductionInput]
}

input ContractUpdateCompensationV2Input {
    schemaName: String
    components: [CompensationComponentInput!]
    grant: [GrantInput]
}

input CompensationComponentInput {
    inputId: String!
    name: String
    currency: String
    billingRateType: String
    billingRate: String
    billingFrequency: String
    payScheduleName: String
    isInstallment: String
    numberOfInstallments: String
    startDate: String
    endDate: String
    notes: String
}

type ContractUpdateCompensationV2Response {
    contract: Contract
    success: Boolean!
    errors: [ComponentErrors]
}

type ComponentErrors {
    inputId: String!
    error: [ComponentError!]
}

type ComponentError {
    key: String!
    value: String
    errors: [String!]!
}

input DeductionInput {
    deductionDefinitionId: ID!
    value: Float!
    unit: String
}

# Domain
input FixedPayComponentInput {
    name: String
    label: String
    amount: Float
    currency: CurrencyCode
    amountType: PayAmountType
    frequency: RateFrequency
    paySchedule: ScheduleTimeInput
    rateType: RateType
    payOn: MonthYearInput
    condition: String
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDateInput]
    instalments: [InstalmentInput!]
}

input PercentPayComponentInput {
    name: String
    label: String
    ratio: Float
    currency: CurrencyCode
    amountType: PayAmountType
    frequency: RateFrequency
    paySchedule: ScheduleTimeInput
    basedOn: FixedPayComponentInput
    payOn: MonthYearInput
    condition: String
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDateInput]
}

input PayFrequencyDateInput {
    identifier: PayFrequencyDateIdentifier
    dateOfMonth: Int
    dayOfWeek: DayOfWeek
}

input InstalmentInput {
    name: String
    label: String
    amount: Float
    payOn: MonthYearInput
    currency: CurrencyCode
}

"""
Except `id`, all are optional.
E.g. 1: to update label and amount: {id: 123, label: "New Label", amount: 5000}
E.g. 2: to update paymentFrequency and isActive: {id: 123, paymentFrequency: "SEMIMONTHLY", isActive: true}
E.g. 3: a useless request that does not update anything: {id: 123}
"""
input CompensationRecordUpdateInput {
    id: ID!                                         # compensation.id
    name: String                                    # there's a set of allowed names, not meant to be any string
    label: String                                   # mostly when you want to change an allowance/variable bonus name etc...
    amount: Float
    currency: CurrencyCode
    amountType: PayAmountType                       # FIXED_AMOUNT / VARIABLE_AMOUNT only. The other values seem deprecated
    frequency: RateFrequency                        # the "unit" of the "amount"
    paySchedule: ScheduleTimeInput                  # seems for 13thMonth/14thMonth/thr only
    rateType: RateType
    payOn: MonthYearInput                           # the month value is in [0, 11] (0 = Jan, 11 = Dec) - a legacy mechanism
    condition: String                               # More details of when/how this compensation is calculated/paid
    paymentFrequency: PayFrequency                  # the frequency of the actual payment that lands to member's bank account
    paymentFrequencyDate: [PayFrequencyDateInput!]  # the array itself can be null/omitted (no change to DB); can be an empty array ([] - the mutation will set to []); if not empty: requires dateOfMonth + identifier
#    isActive: Boolean                              # don't set this, set status instead
    status: CompensationStatus                      # ACTIVE: will set is_active = true; The rest: will set is_active = false (use PENDING to disable a compensation, if you don't know which to use) - giga/omega for context
    validFromInclusive: Date                        # related to salary revision - giga/omega for context
    validToExclusive: Date                          # related to salary revision - giga/omega for context
}

enum CompensationStatus {
    PENDING
    APPROVED
    ACTIVE
    COMPLETED
    UNCHANGED
}

input CompensationRecordInsertInput {
    contractId: ID!
    name: String!                                   # there's a set of allowed names, not meant to be any string
    amount: Float!
    label: String
    currency: CurrencyCode!
    frequency: RateFrequency!                       # the "unit" of the "amount"
    fixedPayComponentInfo: PayComponentInfoInput    # Used for fixedPayComponentInfo
    percentPayComponentInfo: PayComponentInfoInput  # Used for percentPayComponentInfo
    payComponentType: ComponentType
    paySchedule: ScheduleTimeInput
    condition: String                               # More details of when/how this compensation is calculated/paid
    isBase: Boolean
    rateType: RateType
    paymentFrequency: PayFrequency                  # the frequency of the actual payment that lands to member's bank account
    paymentFrequencyDate: [PayFrequencyDateInput!]  # the array itself can be null/omitted (no change to DB); can be an empty array ([] - the mutation will set to []); if not empty: requires dateOfMonth + identifier
    status: CompensationStatus                      # ACTIVE: will set is_active = true; The rest: will set is_active = false (use PENDING to disable a compensation, if you don't know which to use) - giga/omega for context
    validFromInclusive: Date                        # related to salary revision - giga/omega for context
    validToExclusive: Date                          # related to salary revision - giga/omega for context
}

input PayComponentInfoInput {
    basedOnComponentId: ID
    payOn: MonthYearInput                           # the month value is in [0, 11] (0 = Jan, 11 = Dec) - a legacy mechanism;
}

# --------------------------------------
# Query types....
# --------------------------------------

interface CompensationPayComponent {
    id: ID
    name: String
    label: String
    amount: Float
    currency: CurrencyCode @authorize(company: ["view.company.compensation.currency"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    amountType: PayAmountType
    frequency: RateFrequency @authorize(company: ["view.company.compensation.salary-pay-period"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    paySchedule: ScheduleTime
    payOn: MonthYear
    isDeletable: Boolean
    condition: String
    paymentFrequency: PayFrequency @authorize(company: ["view.company.compensation.salary-frequency"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    paymentFrequencyDate: [PayFrequencyDate]
    createdOn: DateTime
    updatedOn: DateTime
}

# Domain
type Compensation {
    payType: PayType    # determines the overall type of pay schedule
    basePay: FixedPayComponent @authorize(company: ["view.company.compensation.base-salary"], operations: ["view.operations.contract"], member: ["view.member.contract"]) # the value returned by this field is either probationBasePay or postProbationBasePay, depends on requested date range
    probationBasePay: FixedPayComponent   @authorize(company: ["view.company.compensation.probation-salary"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    postProbationBasePay: FixedPayComponent @authorize(company: ["view.company.compensation.base-salary"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    other: [FixedPayComponent] @deprecated # deprecating it to support multiple type of pay components using `additionalPays`
    additionalPays: [CompensationPayComponent] @authorize(company: ["view.company.compensation.additional-pay"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    additional: String
    payrollStart: DateTime # TODO: Skipped, will do with HR-member flow... @TR
    grant(contractId: ID!): [Grant] @authorize(company: ["view.company.compensation.grant"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    deductions: [Deduction]
}

input UpdateCompensationPayInfoInput {
    contractId: [ID!]!
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDateInput!]
}

type Deduction {
    deductionDefinitionId: ID!
    value: Float!
    unit: String
}

enum PayType {
    FIXED       # Payments are calculated for a fixed period of time which is defaulted to a Month currently.
    DYNAMIC     # Not calculated for any time period but only determined by member/customer based on other criteria
}

type FixedPayComponent implements CompensationPayComponent {
    id: ID
    name: String
    label: String
    amount: Float
    currency: CurrencyCode @authorize(company: ["view.company.compensation.currency"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    amountType: PayAmountType
    frequency: RateFrequency @authorize(company: ["view.company.compensation.salary-pay-period"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    paySchedule: ScheduleTime
    rateType: RateType
    payOn: MonthYear
    isDeletable: Boolean
    condition: String
    paymentFrequency: PayFrequency @authorize(company: ["view.company.compensation.salary-frequency"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    paymentFrequencyDate: [PayFrequencyDate]
    validFromInclusive: Date @authorize(company: ["view.company.performance.effective-from"], operations: ["view.operations.contract"], member: ["view.member.contract"]) # e.g. 19 Oct is in the range of [19 Oct, 19 Nov)
    validToExclusive: Date   # e.g. 19 Nov is NOT in the range of [19 Oct, 19 Nov)
    createdOn: DateTime
    updatedOn: DateTime
    instalments: [Instalment!]
}

type PercentPayComponent implements CompensationPayComponent {
    id: ID
    name: String
    label: String
    amount: Float
    currency: CurrencyCode
    amountType: PayAmountType
    frequency: RateFrequency
    paySchedule: ScheduleTime
    basedOn: FixedPayComponent
    payOn: MonthYear
    isDeletable: Boolean
    condition: String
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDate]
    createdOn: DateTime
    updatedOn: DateTime
}

type Instalment {
    name: String
    label: String
    amount: Float
    payOn: MonthYear
    currency: CurrencyCode
}
