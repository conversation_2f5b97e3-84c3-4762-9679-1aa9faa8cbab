enum DataType {
    INTEGER,
    BOOLEAN,
    DATE,
    STRI<PERSON>,
    DURATION,
    PERIOD,
    COMPLEX
}
type Component {
    id: ID!
    name : String!
    description : String
    isConsumedComponent : Boolean!
    dataType: DataType!
    schema : String!
}
input ComponentCreateInput {
    name : String!
    description : String
    isConsumedComponent : Boolean!
    dataType: DataType!
    schema : String
}

input ComponentResolveInput {
    name: String!
    scope: ComplianceScopeInput!
    context : JSON
}
input ComponentsResolveInput {
    name: [String!]!
    scope: ComplianceScopeInput!
    context : JSON
}

type Limit {
    min: String
    max: String
    default: String
}

extend type Mutation {
    componentCreate(input: ComponentCreateInput!) : Component!
}

extend type Query {
    componentGetAll: [Component] @authorize(operations: ["view.operations.compliance.component"], company: ["view.company.compliance.component"])
    componentGetById(id: ID!): Component @authorize(operations: ["view.operations.compliance.component"], company: ["view.company.compliance.component"])
    componentsRequiredByComplianceScope(scope: ComplianceScopeInput!): [Component] @authorize(operations: ["view.operations.compliance.component"], company: ["view.company.compliance.component"])
    componentResolve(input: ComponentResolveInput!) : JSON! @authorize(operations: ["view.operations.compliance.component-resolve"], company: ["view.company.compliance.component-resolve"])
    componentsResolve(input: ComponentsResolveInput!) : JSON! @authorize(operations: ["view.operations.compliance.component-resolve"], company: ["view.company.compliance.component-resolve"])
    componentGetLimits(componentName: String!, scope: ComplianceScopeInput!,context: JSON): Limit! @authorize(operations: ["view.operations.compliance.component-resolve"], company: ["view.company.compliance.component-resolve"])
}
