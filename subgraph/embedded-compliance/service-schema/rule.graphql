enum UseCase {
    ONBOARDING,
    OFFBOA<PERSON><PERSON>,
    RES<PERSON><PERSON><PERSON><PERSON>,
    TERMINA<PERSON>ON,
    COMP<PERSON><PERSON><PERSON>ON,
    CONTRACT_RENEWAL,
}

input UserContext {
    userValues: JSON,
    contractId: ID,
    pdfBody: String, #base64 encoded,
    contractPdfPath: String
}

type ComplianceScope {
    country: CountryCode
    subdivision: SubdivisionIsoCode
    useCase: UseCase
}

input ComplianceScopeInput {
    country: CountryCode
    subdivision: SubdivisionIsoCode
    useCase: UseCase
}

type ComplianceRule {
    id: ID!
    name: String!
    description: String
    expression: String!
    ruleScope: ComplianceScope!
    requiredComponents: [Component]!
}
input ComplianceRuleCreateInput {
    name: String!
    description: String
    expression: String!
    ruleScope: ComplianceScopeInput!
}

input ComplianceRuleUpdateInput {
    id: ID!
    name: String
    description: String
    expression: String
    ruleScope: ComplianceScopeInput
}

input ComplianceRuleDeleteInput {
    names: [String],
    ids: [ID],
}

extend type Mutation {
    complianceRuleCreate(input: ComplianceRuleCreateInput!): ComplianceRule! @authorize(operations: ["create.operations.compliance.rule"])
    complianceRuleUpdate(input: ComplianceRuleUpdateInput!): ComplianceRule! @authorize(operations: ["update.operations.compliance.rule"])
    complianceRuleDelete(input: ComplianceRuleDeleteInput!): Boolean! @authorize(operations: ["delete.operations.compliance.rule"])
}

extend type Query {
    complianceRuleGenerate(description: String!): JSON! @authorize(operations: ["generate.operations.compliance.rule"])
    complianceRuleGetByScope(ruleScope: ComplianceScopeInput): [ComplianceRule]! @authorize(operations: ["view.operations.compliance.rule"], company: ["view.company.compliance.rule"])
    complianceRuleGetById(id: ID!): ComplianceRule @authorize(operations: ["view.operations.compliance.rule"], company: ["view.company.compliance.rule"])
    complianceRulesExecute(ids: [ID], ruleScope: ComplianceScopeInput, context: UserContext): JSON! @authorize(operations: ["execute.operations.compliance.rule"])
}
