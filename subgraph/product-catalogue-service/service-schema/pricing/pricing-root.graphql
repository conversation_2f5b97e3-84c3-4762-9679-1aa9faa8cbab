enum ChargeType {
    TIER
    UNIT
    FLAT
    VOLUME
    OVERAGE
    MATRIX
    REFERENCE
}

enum ChargeFrequency {
    ONE_TIME
    MONTHLY
    YEARLY
}

type PricingTier {
    start: Int!
    end: Int
    price: Amount!
    type: TierPriceType!
}

enum TierPriceType {
    FLAT,
    PER_UNIT
}

type AgreementTerm {
    interval: Int
    timeUnit: TimeUnit
}

enum MeteringType {
    G2N_COMPUTED
    PAYMENTS_COMPLETED
    YEAR_END_DOCUMENTS_SUBMITTED
    STATUTORY_DOCUMENTS_FILED
    ACTIVE_MEMBERS
    CONTRACTOR_INVOICES_APPROVED
    LAPTOPS_DISPENSED
    MONITORS_DISPENSED
    ACCESSORIES_DISPENSED
    ITEMS_PICKED_UP_DELIVERED,
    ITEMS_STORED,
    CONTRACTS_CUSTOMISED,
    LEGAL_CONSULTATION_PROVIDED_IN_HOURS
    OFFCYCLE_PAYROLL_RUN_COMPLETED
    STANDARD_BACKGROUND_VERIFICATION_CHECKS_COMPLETED
    HEALTH_INSURANCE_PLAN_ACTIVATIONS
}

enum MatrixFactorType {
    RANGE
    VALUE
}

interface MatrixFactor {
    type: MatrixFactorType!
    meteringUnit: MeteringUnit!
}

type ValueMatrixFactor implements MatrixFactor {
    type: MatrixFactorType!
    meteringUnit: MeteringUnit!
    value: String!
}

type RangeMatrixFactor implements MatrixFactor {
    type: MatrixFactorType!
    meteringUnit: MeteringUnit!
    start: Int!
    end: Int
}

type MatrixEntry {
    matrixFactors: [MatrixFactor!]!
    price: Amount!
}

input MatrixEntryInput {
    matrixFactors: [MatrixFactorInput!]!
    price: AmountInput!
}

input MatrixFactorInput {
    type: MatrixFactorType!
    valueMatrixFactor: ValueMatrixFactorInput
    rangeMatrixFactor: RangeMatrixFactorInput
}

input ValueMatrixFactorInput {
    meteringUnit: MeteringUnitInput!
    value: String!
}

input RangeMatrixFactorInput {
    meteringUnit: MeteringUnitInput!
    start: Int!
    end: Int
}

input AgreementTermInput {
    interval: Int
    timeUnit: TimeUnit
}
