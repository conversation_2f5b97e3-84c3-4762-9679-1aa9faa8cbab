extend type Mutation {
    companyOrderCreate(input: CompanyOrderCreateInput!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderMarkAsSentForSignature(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderMarkAsSigned(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderMarkAsSigningFailed(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderActivateOrder(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderDeactivate(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])
    companyOrderDelete(id: ID!): CompanyOrder! @authorize(operations: ["update.operations.order"])

    companyOrderBundleCreate(input: CompanyOrderBundleCreateInput!): CompanyOrderBundle! @authorize(operations: ["update.operations.order"])
    companyOrderBundleActivate(id: ID!): CompanyOrderBundle! @authorize(operations: ["update.operations.order"])
    companyOrderBundleResume(id: ID!): CompanyOrderBundle! @authorize(operations: ["update.operations.order"])
    companyOrderBundleDeactivate(id: ID!): CompanyOrderBundle! @authorize(operations: ["update.operations.order"])
    companyOrderBundleHold(id: ID!): CompanyOrderBundle! @authorize(operations: ["update.operations.order"])

    companyOrderBackFilling(companyIds: [ID!]!): TaskResponse @authorize(operations: ["backfill.operations.order"])
    companyOrderExtend(input: CompanyOrderExtendInput!): TaskResponse @authorize(operations: ["extend.operations.order"])
}

input CompanyOrderExtendInput {
    orderId: ID!
    additionalProducts: [QuoteProductInput!]!
    signatureStrategy: SignatureStrategy!
}

enum SignatureStrategy {
    FORCE_SIGNING
    SKIP_SIGNING
    PRESERVE
}

extend type Query {
    companyOrders(filter: CompanyOrderFilter, page: PageRequest): CompanyOrderResponse! @authorize(operations: ["view.operations.order"])
    companyOrder(id: ID!): CompanyOrder! @authorize(operations: ["view.operations.order"])
}

input CompanyOrderCreateInput {
    quoteId: ID!
    comment: String
}

input CompanyOrderFilter {
    companyId: ID
    status: CompanyOrderStatus
}

type CompanyOrderResponse {
    companyOrders: [CompanyOrder!]!
    page: PageResult!
}

type CompanyOrder {
    id: ID!
    company: Company!
    status: CompanyOrderStatus!
    billingFrequency: AgreementTerm!
    paymentDuePeriod: AgreementTerm!
    orderTerm: AgreementTerm!
    billingStartDate: DateTime!
    billingEndDate: DateTime
    orderedProducts: [CompanyProduct!]!
    subscription: OrderSubscription
    discount: OrderDiscount
    quote: Quote!
    comment: String
    document: DocumentReadable @deprecated(reason: "use orderDocument")
    orderDocument: Document
}

input CompanyOrderBundleCreateInput {
    companyId: ID!
    description: String
}

type CompanyOrderBundle {
    id: ID!
    company: Company!
    subscribedProducts: [CompanyProduct!]!
    companyOrders: [CompanyOrder!]!
    quotes: [Quote!]
    status: CompanyOrderBundleStatus!
    description: String
}

enum CompanyOrderBundleStatus {
    NOT_STARTED
    ON_HOLD
    ACTIVE
    INACTIVE
}

enum CompanyOrderStatus {
    DRAFT,
    ORDER_SENT_FOR_SIGNATURE,
    ORDER_SIGNED,
    ORDER_SIGNING_FAILED,
    ORDER_ACTIVATED,
    ON_HOLD,
    ORDER_DEACTIVATED,
    DELETED
}
