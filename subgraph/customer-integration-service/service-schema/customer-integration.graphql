extend type Mutation {
    customerIntegrationDisconnect(id: ID): CustomerIntegration @authorize(company: ["view.company.company.has-integration"], operations: ["disconnect.operations.company.integration"])
    changeSyncState(integrationId: ID!, syncType: SyncType!, on: Boolean!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["change.operations.company.sync-state"])
    importEmployees(syncId: String!, employees: [String]!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["import.operations.company.employees"])
    sendImportedMemberInvites(syncId: String, employees: [String]!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["send.operations.company.member-invitations"])
    fetchLatestEmployeesFromPlatform(integrationId: ID!): FetchEmployeesResult @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.external"])
    startIncomingSync(integrationId: ID!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["manual-sync.operations.company.gp"])
    syncEORManually(integrationId: ID!): SyncEORManuallyResult @authorize(company: ["view.company.company.has-integration"], operations: ["manual-sync.operations.company.eor"])
    dismissSyncResult(syncId: String!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["manual-sync.operations.company.eor"])
    clearGPSyncFailedEvents(input: ClearGPSyncFailedEventsInput!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["clear-events.operations.company.gp"])
    clearGPSyncFailedEventsV2(companyId: ID!, platformID: ID!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["clear-events.operations.company.gp"])
    saveIntegrationEntityMappingStatus(entityMappingId: ID!, enableDataSync: Boolean): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["fields-mapping.operations.company.gp"])
    saveLeaveTypesMapping(input: SaveLeaveTypesMappingInput): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    upsertDocumentFolder(input: UpsertDocumentFolderInput): DocumentFolder @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    triggerIntegrationSync(syncId: String, eventId: String, syncType: SyncType!): TaskResponse @authorize(operations: ["view.operations.company.integration"])
    migrateIntegrationIdToContractIntegrationTable(batchSize: Int): TaskResponse @authorize(operations: ["update.operations.company.integration"])
    triggerEORTimeoffSyncManually(integrationId: ID): TaskResponse @authorize(operations: ["update.operations.company.integration"])
}


extend type Query {
    customerIntegrationDefinitions( filters: CustomerIntegrationFilter ): [CustomerIntegrationDefinition] @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    getPositionsForIntegrations( integrationId: ID! ): [Position] @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    companyIntegrationInfo(integrationId: ID!): CompanyIntegrationInfo @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    syncStatus(integrationId: ID!, syncType: SyncType): CompanySyncStatus @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getEmployeesToSendInviteTo(integrationId: ID!): [Employee] @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getLatestEmployeesForImport(integrationId: ID!): FetchEmployeesResult @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getLatestSyncResultForIntegration(integrationId: ID!): LatestSyncResult @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    validateIntegrationCredentials(integrationId: ID!): TaskResponse @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    syncSummaryResultDownload(input: SyncSummaryResultDownloadInput!): SyncSummaryResultDownloadOutput @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getIntegrationEntityMappingStatus(integrationId: ID!): [IntegrationEntityMappingStatusOutput] @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getExternalLeaveTypes(integrationId: ID!): [ExternalLeaveType] @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    getInternalLeaveTypes(companyId: ID!): [ExternalLeaveType] @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    getLeaveTypeMappingDefinition(integrationId: ID!): [LeaveTypeMappingDefinition] @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    getDocumentFolders(type: DocumentFolderType!, integrationId: ID!): DocumentFolder @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    getIntegrationFieldsMappingProfile(integrationId: ID!, entityId: ID!): IntegrationFieldsMappingOutputV2 @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
    getIntegrationFieldsMappingContractorProfile(integrationId: ID!): IntegrationFieldsMappingContractorOutput @authorize(company: ["view.company.company.has-integration"], operations: ["validate.operations.company.integration"])
}


input CustomerIntegrationFilter {
    category: PlatformCategory
}

input SyncSummaryResultDownloadInput {
    syncId: String!
}

input ClearGPSyncFailedEventsInput {
    companyId: ID!
    platformID: ID!
    from: DateTime
    to: DateTime
}

type SyncSummaryResultDownloadOutput {
    isDownloaded: Boolean
    reportFile: DocumentReadable
}

type CustomerIntegrationDefinition {
    id: ID
    name: String
    category: PlatformCategory
    color: String
    logoLink: String
    squareLogoLink: String
}

# Based on the domain Customer Integrations as detailed here:
# https://www.notion.so/usemultiplier/Customer-Integrations-866c08d007594233aae865c9cc8e83cf#e90592e8484b4d28908235240ac9e988
type CustomerIntegration {
    id: ID
    name: String
    category: PlatformCategory
    connected: Boolean
    isPositionDropdownListEnabled: Boolean
}

type SyncEmployees {
    employees: [Employee]
    syncId: ID @deprecated
    extSyncId: String
}

type Employee {
    id: ID @deprecated
    externalId: String
    firstName: String
    lastName: String
}

type CompanyIntegration {
    id: ID
    companyId: ID
    providerId: ID
    platformID: ID
    syncInProgress: Boolean
    lastSyncCreatedAt: DateTime
    lastSyncId: ID
}

type CompanyIntegrationInfo {
    companyId: ID
    providerId: ID
    platformID: ID
    incomingSyncInProgress: Boolean
    outgoingSyncInProgress: Boolean
    lastIncomingSyncStartTime: DateTime
    lastOutgoingSyncStartTime: DateTime
    lastIncomingSyncId: String
    lastOutgoingSyncId: String
    incomingSyncEnabled: Boolean
    outgoingSyncEnabled: Boolean
    timeOffSyncEnabled: Boolean
    incomingImportInProgress: Boolean
    isFetchingFields: Boolean
}

type CompanySync {
    id: ID
    companyId: ID
    status: SyncStatus
    createdAt: DateTime
    updatedAt: DateTime
}

type CompanySyncStatus {
    companyId: ID
    status: SyncStatus
    createdAt: DateTime
    updatedAt: DateTime
}

type Position {
    positionId: String
    designation: String
    department: String
}

type FetchEmployeesResult {
    employees: [Employee]
    syncId: String
}

type SyncEORManuallyResult {
    syncId: String
    integrationId: ID
    success: Boolean
    message: String
}

type EOREmployeesToSyncResult {
    numberOfEmployees: Int
}

type LatestSyncResult {
    syncId: String
    integrationId: ID
    status: SyncStatus
    addedContracts: Int
    updatedContracts: Int
    failedContracts: Int
    completedAt: DateTime
    createdAt: DateTime
    updatedAt: DateTime
    dismissedAt: DateTime
}

type IntegrationFieldsMappingOutputV2 {
    profileId: UUID
    integrationId: ID
    companyId: ID
    entityId: ID
    legalEntity: LegalEntity,
    entityMappingStatus: LegalMappingStatus
    sourceFields: [FieldMappingV2]
    targetFields: [FieldMappingV2]
}

type IntegrationFieldsMappingContractorOutput {
    profileId: UUID
    integrationId: ID
    companyId: ID
    entityMappingStatus: LegalMappingStatus
    sourceFields: [FieldMappingV2]
    targetFields: [FieldMappingV2]
}

type FieldMappingV2{
    key: String
    label: String
    isRequired: Boolean
    children: [FieldMappingV2]
}

type UnmappedField {
    key: String
    label: String
    type: String
    isMappedByThirdParty: Boolean
    fieldId: String
    fieldFromApp: String
    isCustomField: Boolean
    children: [UnmappedField]
    subFields: [UnmappedField]
}

type DocumentFolder {
    folders: [DocumentFolderDetail]
    savedFolder: DocumentFolderDetail
}

type DocumentFolderDetail {
    id: String!
    label: String!
}

type IntegrationEntityMappingStatusOutput {
    entityId: ID @deprecated
    integrationId: ID
    companyId: ID @deprecated
    status: String @deprecated
    isEnabled: Boolean
    entityName: String @deprecated
    legalEntity: LegalEntity
    company: Company
    entityMappingStatus: LegalMappingStatus
    entityMappingId: ID
}

type ExternalLeaveType {
    id: String
    name: String
    leaveType: String
}

type LeaveTypeMappingDefinition {
    id: ID
    integrationId: ID
    externalTypeId: String
    internalTypeId: String
}

input LeaveTypeMappingDefinitionInput {
    id: ID
    integrationId: ID
    externalTypeId: String
    internalTypeId: String
}

input SaveLeaveTypesMappingInput {
    integrationId: ID!
    mapping: [LeaveTypeMappingDefinitionInput]
}

input UpsertDocumentFolderInput {
    integrationId: ID!
    type: DocumentFolderType!
    folderId: String!
}

# Currently reflects merge.dev categories, but should encompass most integration types from other
# unified API providers
enum PlatformCategory {
    HRIS
    ATS
    ACCOUNTING
    TICKETING
    CRM
    EXPENSES
}

enum SyncStatus {
    PENDING
    SUCCESS
    FAILED
    IN_PROGRESS
}

enum SyncType {
    INCOMING,
    OUTGOING,
    MANUAL_OUTGOING,
    TIMEOFF
}

enum LegalMappingStatus {
    FULLY_MAPPED,
    UNMAPPED,
    PARTIALLY_MAPPED
}

enum DocumentFolderType {
    PAYSLIP
}
